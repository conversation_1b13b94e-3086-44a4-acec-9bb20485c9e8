import { createClient } from 'next-sanity'

// Production client with CDN for better performance
export const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  apiVersion: '2024-03-15',
  useCdn: process.env.NODE_ENV === 'production', // Use CDN in production
  perspective: 'published', // Only fetch published content
  stega: {
    enabled: false, // Disable visual editing for better performance
  },
})

// Preview client for draft content (when needed)
export const previewClient = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || '',
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || 'production',
  apiVersion: '2024-03-15',
  useCdn: false,
  perspective: 'previewDrafts',
  ...(process.env.SANITY_API_READ_TOKEN && { token: process.env.SANITY_API_READ_TOKEN }),
})